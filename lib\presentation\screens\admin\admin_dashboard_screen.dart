import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../widgets/common/custom_button.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  int _selectedTimeRange = 0; // 0: Today, 1: This Week, 2: This Month

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Navigate to admin settings
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time Range Selector
            _buildTimeRangeSelector(),
            
            const SizedBox(height: AppConstants.spacingL),
            
            // Overview Cards
            _buildOverviewCards(),
            
            const SizedBox(height: AppConstants.spacingL),
            
            // Attendance Chart
            _buildAttendanceChart(),
            
            const SizedBox(height: AppConstants.spacingL),
            
            // Employee Status
            _buildEmployeeStatus(),
            
            const SizedBox(height: AppConstants.spacingL),
            
            // Quick Actions
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeRangeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingS),
        child: Row(
          children: [
            Expanded(
              child: _buildTimeRangeButton('Today', 0),
            ),
            Expanded(
              child: _buildTimeRangeButton('This Week', 1),
            ),
            Expanded(
              child: _buildTimeRangeButton('This Month', 2),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeRangeButton(String text, int index) {
    final isSelected = _selectedTimeRange == index;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.spacingXS),
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedTimeRange = index;
          });
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? AppColors.primaryBlue : AppColors.grey100,
          foregroundColor: isSelected ? AppColors.white : AppColors.grey600,
          elevation: isSelected ? 2 : 0,
        ),
        child: Text(text),
      ),
    );
  }

  Widget _buildOverviewCards() {
    return Row(
      children: [
        Expanded(
          child: _buildOverviewCard(
            'Total Employees',
            '24',
            Icons.people,
            AppColors.primaryBlue,
          ),
        ),
        const SizedBox(width: AppConstants.spacingS),
        Expanded(
          child: _buildOverviewCard(
            'Present Today',
            '18',
            Icons.check_circle,
            AppColors.success,
          ),
        ),
        const SizedBox(width: AppConstants.spacingS),
        Expanded(
          child: _buildOverviewCard(
            'Absent',
            '6',
            Icons.cancel,
            AppColors.error,
          ),
        ),
        const SizedBox(width: AppConstants.spacingS),
        Expanded(
          child: _buildOverviewCard(
            'Late Arrivals',
            '3',
            Icons.schedule,
            AppColors.warning,
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: AppConstants.spacingS),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: AppColors.grey600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Attendance Trends',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingM),
            
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: false),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                          if (value.toInt() >= 0 && value.toInt() < days.length) {
                            return Text(days[value.toInt()]);
                          }
                          return const Text('');
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: [
                        const FlSpot(0, 20),
                        const FlSpot(1, 22),
                        const FlSpot(2, 18),
                        const FlSpot(3, 24),
                        const FlSpot(4, 19),
                        const FlSpot(5, 15),
                        const FlSpot(6, 12),
                      ],
                      isCurved: true,
                      color: AppColors.primaryBlue,
                      barWidth: 3,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppColors.primaryBlue.withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Employee Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to full employee list
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingM),
            
            // Mock employee data
            _buildEmployeeStatusItem('John Doe', 'Present', '09:00 AM', AppColors.success),
            _buildEmployeeStatusItem('Jane Smith', 'Present', '08:45 AM', AppColors.success),
            _buildEmployeeStatusItem('Mike Johnson', 'Late', '09:30 AM', AppColors.warning),
            _buildEmployeeStatusItem('Sarah Wilson', 'Absent', '--', AppColors.error),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeStatusItem(String name, String status, String time, Color statusColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.spacingXS),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: AppColors.grey200,
            child: Text(
              name.substring(0, 1),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
          ),
          
          const SizedBox(width: AppConstants.spacingM),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  time,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
          
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.spacingS,
              vertical: AppConstants.spacingXS,
            ),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingM),
            
            Row(
              children: [
                Expanded(
                  child: SecondaryButton(
                    text: 'Export Report',
                    icon: Icons.download,
                    onPressed: () {
                      // TODO: Export functionality
                    },
                  ),
                ),
                const SizedBox(width: AppConstants.spacingS),
                Expanded(
                  child: SecondaryButton(
                    text: 'Send Notifications',
                    icon: Icons.notifications,
                    onPressed: () {
                      // TODO: Send notifications
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingS),
            
            Row(
              children: [
                Expanded(
                  child: SecondaryButton(
                    text: 'Manage Employees',
                    icon: Icons.people_alt,
                    onPressed: () {
                      // TODO: Navigate to employee management
                    },
                  ),
                ),
                const SizedBox(width: AppConstants.spacingS),
                Expanded(
                  child: SecondaryButton(
                    text: 'View Reports',
                    icon: Icons.analytics,
                    onPressed: () {
                      // TODO: Navigate to reports
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
