import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../core/constants/app_constants.dart';

class AttendanceService {
  Future<Map<String, dynamic>> checkIn({
    required double latitude,
    required double longitude,
    required String location,
    String? notes,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConstants.baseUrl}/attendance/checkin'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${AppConstants.tokenKey}',
        },
        body: jsonEncode({
          'latitude': latitude,
          'longitude': longitude,
          'location': location,
          'notes': notes,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      ).timeout(Duration(milliseconds: AppConstants.requestTimeout));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'attendance': data['attendance'],
          'message': 'Check-in réussi'
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur lors du check-in: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }

  Future<Map<String, dynamic>> checkOut({
    required double latitude,
    required double longitude,
    required String location,
    String? notes,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConstants.baseUrl}/attendance/checkout'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${AppConstants.tokenKey}',
        },
        body: jsonEncode({
          'latitude': latitude,
          'longitude': longitude,
          'location': location,
          'notes': notes,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      ).timeout(Duration(milliseconds: AppConstants.requestTimeout));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'attendance': data['attendance'],
          'message': 'Check-out réussi'
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur lors du check-out: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }
}

