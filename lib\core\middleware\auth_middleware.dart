import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/repositories/auth_repository.dart';
import '../../data/models/user_model.dart';

/// Middleware pour gérer l'authentification et les autorisations
class AuthMiddleware {
  
  /// Vérifie si l'utilisateur est authentifié
  static bool isAuthenticated(BuildContext context) {
    final authRepo = context.read<AuthRepository>();
    return authRepo.isAuthenticated;
  }

  /// Vérifie si l'utilisateur a le rôle requis
  static bool hasRole(BuildContext context, UserRole requiredRole) {
    final authRepo = context.read<AuthRepository>();
    if (!authRepo.isAuthenticated || authRepo.currentUser == null) {
      return false;
    }

    final userRole = UserRole.fromString(authRepo.currentUser!.role);
    return userRole == requiredRole;
  }

  /// Vérifie si l'utilisateur a l'un des rôles requis
  static bool hasAnyRole(BuildContext context, List<UserRole> requiredRoles) {
    final authRepo = context.read<AuthRepository>();
    if (!authRepo.isAuthenticated || authRepo.currentUser == null) {
      return false;
    }

    final userRole = UserRole.fromString(authRepo.currentUser!.role);
    return requiredRoles.contains(userRole);
  }

  /// Vérifie si l'utilisateur a des privilèges administratifs
  static bool hasAdminPrivileges(BuildContext context) {
    final authRepo = context.read<AuthRepository>();
    return authRepo.hasAdminPrivileges;
  }

  /// Redirige vers la page de connexion si non authentifié
  static void requireAuth(BuildContext context) {
    if (!isAuthenticated(context)) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        '/login',
        (route) => false,
      );
    }
  }

  /// Redirige vers la page d'accueil appropriée selon le rôle
  static void redirectToHome(BuildContext context) {
    final authRepo = context.read<AuthRepository>();
    final homeRoute = authRepo.getHomeRouteForUser();
    Navigator.pushNamedAndRemoveUntil(
      context,
      homeRoute,
      (route) => false,
    );
  }

  /// Vérifie l'autorisation et redirige si nécessaire
  static bool checkPermissionAndRedirect(
    BuildContext context,
    UserRole requiredRole, {
    String? redirectRoute,
  }) {
    if (!isAuthenticated(context)) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        '/login',
        (route) => false,
      );
      return false;
    }

    if (!hasRole(context, requiredRole)) {
      final route = redirectRoute ?? '/unauthorized';
      Navigator.pushNamedAndRemoveUntil(
        context,
        route,
        (route) => false,
      );
      return false;
    }

    return true;
  }
}

/// Widget pour protéger les routes selon l'authentification
class AuthGuard extends StatelessWidget {
  final Widget child;
  final bool requireAuth;
  final UserRole? requiredRole;
  final List<UserRole>? requiredRoles;
  final Widget? fallback;
  final String? redirectRoute;

  const AuthGuard({
    super.key,
    required this.child,
    this.requireAuth = true,
    this.requiredRole,
    this.requiredRoles,
    this.fallback,
    this.redirectRoute,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthRepository>(
      builder: (context, authRepo, _) {
        // Vérifier l'authentification
        if (requireAuth && !authRepo.isAuthenticated) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.pushNamedAndRemoveUntil(
              context,
              '/login',
              (route) => false,
            );
          });
          return fallback ?? const Center(child: CircularProgressIndicator());
        }

        // Vérifier le rôle spécifique
        if (requiredRole != null) {
          if (!AuthMiddleware.hasRole(context, requiredRole!)) {
            return _buildUnauthorized(context);
          }
        }

        // Vérifier les rôles multiples
        if (requiredRoles != null && requiredRoles!.isNotEmpty) {
          if (!AuthMiddleware.hasAnyRole(context, requiredRoles!)) {
            return _buildUnauthorized(context);
          }
        }

        return child;
      },
    );
  }

  Widget _buildUnauthorized(BuildContext context) {
    if (fallback != null) {
      return fallback!;
    }

    if (redirectRoute != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.pushNamedAndRemoveUntil(
          context,
          redirectRoute!,
          (route) => false,
        );
      });
      return const Center(child: CircularProgressIndicator());
    }

    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Accès non autorisé',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Vous n\'avez pas les permissions nécessaires pour accéder à cette page.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget pour afficher du contenu selon le rôle
class RoleBasedWidget extends StatelessWidget {
  final UserRole? requiredRole;
  final List<UserRole>? requiredRoles;
  final Widget child;
  final Widget? fallback;

  const RoleBasedWidget({
    super.key,
    this.requiredRole,
    this.requiredRoles,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    bool hasPermission = false;

    if (requiredRole != null) {
      hasPermission = AuthMiddleware.hasRole(context, requiredRole!);
    } else if (requiredRoles != null && requiredRoles!.isNotEmpty) {
      hasPermission = AuthMiddleware.hasAnyRole(context, requiredRoles!);
    } else {
      hasPermission = AuthMiddleware.isAuthenticated(context);
    }

    if (hasPermission) {
      return child;
    }

    return fallback ?? const SizedBox.shrink();
  }
}

/// Widget pour les fonctionnalités admin uniquement
class AdminOnlyWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const AdminOnlyWidget({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleBasedWidget(
      requiredRoles: const [UserRole.admin, UserRole.manager],
      fallback: fallback,
      child: child,
    );
  }
}

/// Widget pour les employés uniquement
class EmployeeOnlyWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const EmployeeOnlyWidget({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleBasedWidget(
      requiredRole: UserRole.employee,
      fallback: fallback,
      child: child,
    );
  }
}
