import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/repositories/attendance_repository.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_overlay.dart';

class CheckInScreen extends StatefulWidget {
  const CheckInScreen({super.key});

  @override
  State<CheckInScreen> createState() => _CheckInScreenState();
}

class _CheckInScreenState extends State<CheckInScreen> {
  final TextEditingController _notesController = TextEditingController();
  bool _isLocationLoading = false;
  String? _currentLocation;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Check In/Out'),
        centerTitle: true,
      ),
      body: Consumer<AttendanceRepository>(
        builder: (context, attendanceRepo, child) {
          return LoadingOverlay(
            isLoading: attendanceRepo.isLoading,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Current Time Card
                  _buildCurrentTimeCard(),
                  
                  const SizedBox(height: AppConstants.spacingL),
                  
                  // Location Card
                  _buildLocationCard(),
                  
                  const SizedBox(height: AppConstants.spacingL),
                  
                  // Today's Status
                  _buildTodayStatusCard(attendanceRepo),
                  
                  const SizedBox(height: AppConstants.spacingL),
                  
                  // Notes Section
                  _buildNotesSection(),
                  
                  const SizedBox(height: AppConstants.spacingXL),
                  
                  // Check In/Out Button
                  _buildActionButton(attendanceRepo),
                  
                  // Error Message
                  if (attendanceRepo.errorMessage != null) ...[
                    const SizedBox(height: AppConstants.spacingM),
                    _buildErrorMessage(attendanceRepo.errorMessage!),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentTimeCard() {
    final now = DateTime.now();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          children: [
            Text(
              DateFormat('EEEE, MMMM d, yyyy').format(now),
              style: TextStyle(
                fontSize: 16,
                color: AppColors.grey600,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingS),
            
            Text(
              DateFormat('HH:mm:ss').format(now),
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.location_on,
                  color: AppColors.primaryBlue,
                ),
                const SizedBox(width: AppConstants.spacingS),
                const Text(
                  'Current Location',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (_isLocationLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: _getCurrentLocation,
                  ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingS),
            
            Text(
              _currentLocation ?? 'Getting location...',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.grey600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodayStatusCard(AttendanceRepository attendanceRepo) {
    final attendance = attendanceRepo.todayAttendance;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Today\'s Status',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingM),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    'Check-in',
                    attendance?.checkinTime != null
                        ? DateFormat('HH:mm').format(attendance!.checkinTime!)
                        : '--:--',
                    Icons.login,
                    attendance?.checkinTime != null ? AppColors.success : AppColors.grey400,
                  ),
                ),
                
                const SizedBox(width: AppConstants.spacingM),
                
                Expanded(
                  child: _buildStatusItem(
                    'Check-out',
                    attendance?.checkoutTime != null
                        ? DateFormat('HH:mm').format(attendance!.checkoutTime!)
                        : '--:--',
                    Icons.logout,
                    attendance?.checkoutTime != null ? AppColors.error : AppColors.grey400,
                  ),
                ),
              ],
            ),
            
            if (attendance?.checkinTime != null && attendance?.checkoutTime == null) ...[
              const SizedBox(height: AppConstants.spacingM),
              Container(
                padding: const EdgeInsets.all(AppConstants.spacingS),
                decoration: BoxDecoration(
                  color: AppColors.info.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.access_time, color: AppColors.info),
                    const SizedBox(width: AppConstants.spacingS),
                    Text(
                      'Working since ${DateFormat('HH:mm').format(attendance!.checkinTime!)}',
                      style: const TextStyle(
                        color: AppColors.info,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingS),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        children: [
          Icon(icon, color: color),
          const SizedBox(height: AppConstants.spacingXS),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.grey600,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notes (Optional)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingS),
            
            TextField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'Add any notes about your check-in/out...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(AttendanceRepository attendanceRepo) {
    final isCheckedIn = attendanceRepo.isCheckedIn;
    
    return isCheckedIn
        ? ErrorButton(
            text: 'Check Out',
            icon: Icons.logout,
            onPressed: () => _handleCheckOut(attendanceRepo),
          )
        : SuccessButton(
            text: 'Check In',
            icon: Icons.login,
            onPressed: () => _handleCheckIn(attendanceRepo),
          );
  }

  Widget _buildErrorMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingM),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.error),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: AppColors.error),
          const SizedBox(width: AppConstants.spacingS),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLocationLoading = true;
    });

    try {
      // TODO: Implement actual location fetching
      await Future.delayed(const Duration(seconds: 2));
      setState(() {
        _currentLocation = 'Office Building, 123 Main St, City';
      });
    } catch (e) {
      setState(() {
        _currentLocation = 'Unable to get location';
      });
    } finally {
      setState(() {
        _isLocationLoading = false;
      });
    }
  }

  Future<void> _handleCheckIn(AttendanceRepository attendanceRepo) async {
    final success = await attendanceRepo.checkIn(
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(AppConstants.checkinSuccess),
          backgroundColor: AppColors.success,
        ),
      );
      _notesController.clear();
    }
  }

  Future<void> _handleCheckOut(AttendanceRepository attendanceRepo) async {
    final success = await attendanceRepo.checkOut(
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(AppConstants.checkoutSuccess),
          backgroundColor: AppColors.success,
        ),
      );
      _notesController.clear();
    }
  }
}
