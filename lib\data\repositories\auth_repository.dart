import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';

class AuthRepository extends ChangeNotifier {
  bool _isAuthenticated = false;
  String? _token;
  
  bool get isAuthenticated => _isAuthenticated;
  String? get token => _token;
  
  Future<void> checkAuthStatus() async {
    final prefs = await SharedPreferences.getInstance();
    _token = prefs.getString(AppConstants.tokenKey);
    _isAuthenticated = _token != null;
    notifyListeners();
  }
  
  Future<bool> login(String email, String password) async {
    // TODO: Implement actual API call
    await Future.delayed(const Duration(seconds: 2));
    
    _token = 'dummy_token';
    _isAuthenticated = true;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.tokenKey, _token!);
    
    notifyListeners();
    return true;
  }
  
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.tokenKey);
    
    _token = null;
    _isAuthenticated = false;
    notifyListeners();
  }
}


