import 'package:flutter/foundation.dart';
import '../services/attendance_service.dart';
import '../services/location_service.dart';

class AttendanceRepository extends ChangeNotifier {
  final AttendanceService _attendanceService = AttendanceService();
  final LocationService _locationService = LocationService();
  
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic>? _todayAttendance;
  bool _isCheckedIn = false;

  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get errorMessage => _error;
  Map<String, dynamic>? get todayAttendance => _todayAttendance;
  bool get isCheckedIn => _isCheckedIn;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  Future<bool> checkIn({String? notes}) async {
    _setLoading(true);
    _clearError();

    try {
      final locationResult = await _locationService.getCurrentPosition();
      
      if (!locationResult['success']) {
        _setError(locationResult['message']);
        return false;
      }

      final result = await _attendanceService.checkIn(
        latitude: locationResult['latitude'],
        longitude: locationResult['longitude'],
        location: locationResult['location'],
        notes: notes,
      );
      
      if (result['success']) {
        _todayAttendance = result['attendance'];
        _isCheckedIn = true;
        notifyListeners();
        return true;
      } else {
        _setError(result['message']);
        return false;
      }
    } catch (e) {
      _setError('Check-in échoué: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> checkOut({String? notes}) async {
    _setLoading(true);
    _clearError();

    try {
      final locationResult = await _locationService.getCurrentPosition();
      
      if (!locationResult['success']) {
        _setError(locationResult['message']);
        return false;
      }

      final result = await _attendanceService.checkOut(
        latitude: locationResult['latitude'],
        longitude: locationResult['longitude'],
        location: locationResult['location'],
        notes: notes,
      );
      
      if (result['success']) {
        _todayAttendance = result['attendance'];
        notifyListeners();
        return true;
      } else {
        _setError(result['message']);
        return false;
      }
    } catch (e) {
      _setError('Check-out échoué: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }
}

