import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../../core/config/app_config.dart';
import '../../core/config/api_endpoints.dart';
import '../../core/utils/exceptions.dart';

/// Service HTTP pour gérer les communications avec l'API Laravel
class HttpService {
  static final HttpService _instance = HttpService._internal();
  factory HttpService() => _instance;
  HttpService._internal();

  final http.Client _client = http.Client();
  String? _authToken;

  /// Définit le token d'authentification
  void setAuthToken(String? token) {
    _authToken = token;
  }

  /// Récupère le token d'authentification actuel
  String? get authToken => _authToken;

  /// Headers par défaut avec authentification si disponible
  Map<String, String> get _headers {
    if (_authToken != null) {
      return ApiHeaders.withAuth(_authToken!);
    }
    return ApiHeaders.defaultHeaders;
  }

  /// Construit l'URL complète
  String _buildUrl(String endpoint) {
    final baseUrl = AppConfig.baseUrl;
    return '$baseUrl$endpoint';
  }

  /// Requête GET
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, dynamic>? params,
  }) async {
    try {
      final url = _buildUrl(ApiEndpoints.buildUrl(endpoint, params: params));
      final uri = Uri.parse(url);

      final response = await _client
          .get(uri, headers: _headers)
          .timeout(AppConfig.requestTimeoutDuration);

      return _handleResponse(response);
    } on SocketException {
      throw NetworkException('Pas de connexion internet');
    } on HttpException catch (e) {
      throw NetworkException('Erreur HTTP: ${e.message}');
    } catch (e) {
      throw ApiException('Erreur lors de la requête GET: $e');
    }
  }

  /// Requête POST
  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? params,
  }) async {
    try {
      final url = _buildUrl(ApiEndpoints.buildUrl(endpoint, params: params));
      final uri = Uri.parse(url);

      final response = await _client
          .post(
            uri,
            headers: _headers,
            body: data != null ? jsonEncode(data) : null,
          )
          .timeout(AppConfig.requestTimeoutDuration);

      return _handleResponse(response);
    } on SocketException {
      throw NetworkException('Pas de connexion internet');
    } on HttpException catch (e) {
      throw NetworkException('Erreur HTTP: ${e.message}');
    } catch (e) {
      throw ApiException('Erreur lors de la requête POST: $e');
    }
  }

  /// Requête PUT
  Future<Map<String, dynamic>> put(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? params,
  }) async {
    try {
      final url = _buildUrl(ApiEndpoints.buildUrl(endpoint, params: params));
      final uri = Uri.parse(url);

      final response = await _client
          .put(
            uri,
            headers: _headers,
            body: data != null ? jsonEncode(data) : null,
          )
          .timeout(AppConfig.requestTimeoutDuration);

      return _handleResponse(response);
    } on SocketException {
      throw NetworkException('Pas de connexion internet');
    } on HttpException catch (e) {
      throw NetworkException('Erreur HTTP: ${e.message}');
    } catch (e) {
      throw ApiException('Erreur lors de la requête PUT: $e');
    }
  }

  /// Requête DELETE
  Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, dynamic>? params,
  }) async {
    try {
      final url = _buildUrl(ApiEndpoints.buildUrl(endpoint, params: params));
      final uri = Uri.parse(url);

      final response = await _client
          .delete(uri, headers: _headers)
          .timeout(AppConfig.requestTimeoutDuration);

      return _handleResponse(response);
    } on SocketException {
      throw NetworkException('Pas de connexion internet');
    } on HttpException catch (e) {
      throw NetworkException('Erreur HTTP: ${e.message}');
    } catch (e) {
      throw ApiException('Erreur lors de la requête DELETE: $e');
    }
  }

  /// Upload de fichier (multipart)
  Future<Map<String, dynamic>> uploadFile(
    String endpoint,
    String fieldName,
    String filePath, {
    Map<String, String>? additionalFields,
  }) async {
    try {
      final url = _buildUrl(endpoint);
      final uri = Uri.parse(url);

      final request = http.MultipartRequest('POST', uri);
      
      // Headers (sans Content-Type pour multipart)
      final headers = Map<String, String>.from(_headers);
      headers.remove(ApiHeaders.contentType);
      request.headers.addAll(headers);

      // Fichier
      request.files.add(await http.MultipartFile.fromPath(fieldName, filePath));

      // Champs additionnels
      if (additionalFields != null) {
        request.fields.addAll(additionalFields);
      }

      final streamedResponse = await request.send()
          .timeout(AppConfig.requestTimeoutDuration);
      
      final response = await http.Response.fromStream(streamedResponse);
      return _handleResponse(response);
    } on SocketException {
      throw NetworkException('Pas de connexion internet');
    } on HttpException catch (e) {
      throw NetworkException('Erreur HTTP: ${e.message}');
    } catch (e) {
      throw ApiException('Erreur lors de l\'upload: $e');
    }
  }

  /// Gestion des réponses HTTP
  Map<String, dynamic> _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    
    try {
      final Map<String, dynamic> data = jsonDecode(response.body);
      
      if (statusCode >= 200 && statusCode < 300) {
        // Succès
        return data;
      } else {
        // Erreur
        final message = data['message'] ?? 
                       data['error'] ?? 
                       ApiErrorMessages.getErrorMessage(statusCode);
        
        switch (statusCode) {
          case ApiStatusCodes.unauthorized:
            throw UnauthorizedException(message);
          case ApiStatusCodes.forbidden:
            throw ForbiddenException(message);
          case ApiStatusCodes.notFound:
            throw NotFoundException(message);
          case ApiStatusCodes.unprocessableEntity:
            throw ValidationException(message, data['errors']);
          case ApiStatusCodes.internalServerError:
            throw ServerException(message);
          default:
            throw ApiException('$message (Code: $statusCode)');
        }
      }
    } catch (FormatException) {
      // Réponse non-JSON
      if (statusCode >= 200 && statusCode < 300) {
        return {'message': 'Succès', 'data': response.body};
      } else {
        throw ApiException('Réponse invalide du serveur (Code: $statusCode)');
      }
    }
  }

  /// Test de connectivité
  Future<bool> checkConnectivity() async {
    try {
      final response = await get(ApiEndpoints.health);
      return response['status'] == 'ok';
    } catch (e) {
      return false;
    }
  }

  /// Nettoyage des ressources
  void dispose() {
    _client.close();
  }
}

/// Extension pour les requêtes avec ID
extension HttpServiceExtensions on HttpService {
  /// GET avec ID
  Future<Map<String, dynamic>> getById(String endpoint, dynamic id) {
    return get(ApiEndpoints.buildUrlWithId(endpoint, id));
  }

  /// PUT avec ID
  Future<Map<String, dynamic>> putById(
    String endpoint,
    dynamic id, {
    Map<String, dynamic>? data,
  }) {
    return put(ApiEndpoints.buildUrlWithId(endpoint, id), data: data);
  }

  /// DELETE avec ID
  Future<Map<String, dynamic>> deleteById(String endpoint, dynamic id) {
    return delete(ApiEndpoints.buildUrlWithId(endpoint, id));
  }

  /// GET avec ID et sous-ressource
  Future<Map<String, dynamic>> getByIdAndResource(
    String endpoint,
    dynamic id,
    String resource, {
    Map<String, dynamic>? params,
  }) {
    return get(
      ApiEndpoints.buildUrlWithIdAndResource(endpoint, id, resource),
      params: params,
    );
  }
}
